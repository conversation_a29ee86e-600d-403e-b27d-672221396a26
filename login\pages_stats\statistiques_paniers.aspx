﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Principale.Master" AutoEventWireup="true"
    CodeBehind="statistiques_paniers.aspx.cs" Inherits="login.pages_stats.statistiques_paniers"
    Culture="auto" meta:resourcekey="PageResource1" UICulture="auto" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">

         <div class="col-lg-4 col-lg-offset-2">
        <div class="form-horizontal">
            <div class="form-group">
                <asp:Label CssClass="col-lg-3 control-label" for="fromDate" ID="lblFromDate" Text="Date de début"
                    runat="server" meta:resourcekey="lblFromDateResource1"></asp:Label>
                <div class="controls">
                    <input type="text" id="fromDate" />
                </div>
            </div>
            <div class="form-group">
                <asp:Label CssClass="col-lg-3 control-label" for="transactionID" ID="lblTransactionID" Text="N° transaction"
                    runat="server" meta:resourcekey="lblTransactionIDResource1"></asp:Label>
                <div class="controls">
                    <input type="text" id="transactionID" />
                </div>
            </div>
            <div class="form-group">
                <asp:Label CssClass="col-lg-3 control-label" for="customerID" ID="lblCustomerID" Text="N° de Client"
                    runat="server" meta:resourcekey="lblCustomerIDResource1"></asp:Label>
                <div class="controls">
                    <input type="text" id="customerID" />
                </div>
            </div>
        </div>
    </div>

     <div class="col-lg-6">
          <div class="form-horizontal">
                    <div class="form-group">
                        <asp:Label CssClass="col-lg-3 control-label" for="toDate" ID="lblToDate" Text="Date de fin"
                            runat="server" meta:resourcekey="lblToDateResource1"></asp:Label>
                        <div class="controls">
                            <input type="text" id="toDate" />
                        </div>
                    </div>
                    <div class="form-group">
                        <asp:Label CssClass="col-lg-3 control-label" for="orderID" ID="lblOrderID" Text="N° de Commande"
                            runat="server" meta:resourcekey="lblOrderIDResource1"></asp:Label>
                        <div class="controls">
                            <input type="text" id="orderID" />
                        </div>
                    </div>
                    <div class="form-group">
                        <asp:Label CssClass="col-lg-3 control-label" for="basketState" ID="lblBasketState" Text="Etat Panier"
                            runat="server" meta:resourcekey="lblBasketStateResource1"></asp:Label>
                        <div class="controls">
                            <asp:DropDownList CssClass="selectpicker" data-style="btn-primary success" ID="basketState"
                                runat="server" meta:resourcekey="basketStateResource1">
                                <asp:ListItem Value="T" Selected="True" Text="Tous" meta:resourcekey="ListItemResource1" />
                                <asp:ListItem Value="V" Text="Validé" meta:resourcekey="ListItemResource2" />
                                <asp:ListItem Value="P" Text="Payé" meta:resourcekey="ListItemResource3" />
                                <asp:ListItem Value="R" Text="Réservé" meta:resourcekey="ListItemResource4" />
                                <asp:ListItem Value="I" Text="Invalide" meta:resourcekey="ListItemResource5" />
                                <asp:ListItem Value="C" Text="Créé" meta:resourcekey="ListItemResource6" />
                            </asp:DropDownList>
                        </div>
                    </div>
                </div>
     </div>
    


       <div class="col-lg-9 col-lg-offset-2 ">
   
         <asp:Button type="submit" class="btn" ID="btnSearch" runat="server" OnClientClick="return false;"
               Text="Valider" UseSubmitBehavior="False" meta:resourcekey="btnSearchResource1" />
       </div>

        <div class="col-lg col-lg-12">
            <div id="error">
                <asp:Label ID="lblErrorDate" CssClass="hidden" Text="" runat="server" meta:resourcekey="lblErrorDateResource1"></asp:Label>
                <asp:Label ID="lblError" CssClass="hidden" runat="server" meta:resourcekey="lblErrorResource1"></asp:Label>
            </div>
        </div>
        <!--Fin div id=date -->

          <div class="right ">
     <input type="button" value="export pdf" id="pdfExport" class="btn btn-success" />
     </div>
        
    <div class="col-lg col-lg-12">

        <div id="datatableResult" class="table-responsive">
            <table id="tableSearch" width="100%" class="table table-striped table-bordered table-hover dataTable">
                <thead>
                    <tr>
                        <th>
                        </th>
                        <th>
                            <asp:Label ID="lblID" Text="ID" runat="server" meta:resourcekey="lblIDResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblBasketID" Text="panier id" runat="server" meta:resourcekey="lblBasketIDResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblStructureID" Text="structure id" runat="server" meta:resourcekey="lblStructureIDResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblDateOpe" Text="date opération" runat="server" meta:resourcekey="lblDateOpeResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblState" Text="etat" runat="server" meta:resourcekey="lblStateResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblCommandID" Text="commande id" runat="server" meta:resourcekey="lblCommandIDResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblTransactionIDTitle" Text="transaction id" runat="server" meta:resourcekey="lblTransactionIDTitleResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblCertificate" Text="certificat" runat="server" meta:resourcekey="lblCertificateResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblCardNumber" Text="N° carte" runat="server" meta:resourcekey="lblCardNumberResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblCardType" Text="type carte" runat="server" meta:resourcekey="lblCardTypeResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblMail" Text="email" runat="server" meta:resourcekey="lblMailResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblDatePaiement" Text="date paiement" runat="server" meta:resourcekey="lblDatePaiementResource1"></asp:Label>
                        </th>
                        <th>
                            <asp:Label ID="lblNumeroBillets" Text="N° billets" runat="server" meta:resourcekey="lblNumeroBilletsResource1"></asp:Label>
                        </th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    
     
    </div>


 

    <script src="../assets/js/date-time/jquery.datetimepicker.js" type="text/javascript"></script>
    <script src="../assets/js/date-time/dataTimePickerCustom.js" type="text/javascript"></script>
    <%--<script src="../assets/js/jquery.dataTables.min.js" type="text/javascript"></script>--%>
    <script src="../assets/js/jquery.dataTables.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.dataTables.bootstrap.js" type="text/javascript"></script>

    <%--<script src="../assets/media/js/ZeroClipboard.js" type="text/javascript"></script>--%>
    <script src="../assets/media/js/TableTools.js" type="text/javascript"></script>
    <script type="text/javascript">
        var bTestFirstTime = true;
        var oTable;
        var sAjaxSourceUrl = "statistiques_paniers.aspx/";

        $(document).ready(function () {
            $('#datatableResult').hide();
            $('#pdfExport').hide();
            
            //Désactive le second input' par défaut
            $('#toDate').prop('disabled', true);

            var logic = function (currentDateTime) {

                //attribue au input "toDate" la date saisie en minimum et en maximum celle d'aujourd'hui
                $('#toDate').datetimepicker({
                    minDate: currentDateTime,
                    maxDate: '0'
                });

                //Active le second input' par défaut au changement de la 1ere
                $('#toDate').prop('disabled', false);

                //Si la valeur saisie est inférieur à la valeur du 2ème input on le vide
                if (currentDateTime < $('#toDate').val()) {
                    $('#toDate').val("");
                } else {
                    //récupère la date d'aujourd'hui
                    $("#toDate").datepicker("setDate", new Date());
                    //récupère la langue situé dans le champs caché
                    var lang = $("#dateLang").val();
                    //convertit le format de date
                    $.datepicker.setDefaults($.datepicker.regional[lang]);
                }
            };

            $('#fromDate').datetimepicker({
                onChangeDateTime: logic,
                onClose: logic
            });

        });



        $("#pdfExport").on('click', function () {

            var sData = JSON.stringify({ status: oTable.html(), strFromDate: $("#fromDate").val(), strToDate: $("#toDate").val(), strTransactionID: $("#transactionID").val(), strOrderID: $("#orderID").val(), strCustomerID: $("#customerID").val(), strBasketState: $('#<%= basketState.ClientID %> :selected').text() });

            $.ajax({
                type: "POST",
                url: sAjaxSourceUrl + 'DownloadPDFFile',
                data: sData,
                contentType: 'application/json; charset=utf-8',
                dataType: "json",
                success: function (response) {                
                    window.open("pdf/" + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no')
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert('Error occurred form PDF file :' + XMLHttpRequest.statusText);
                }
            });

        });


        $('#<%= btnSearch.ClientID %>').click(function () {
           
            if ($("#fromDate").val() == "" || $("#toDate").val() == "") {
                ShowError("error", $('#<%= lblErrorDate.ClientID %>').text(), "alert alert-danger alert-dismissable");
            }
            else {
                if (bTestFirstTime) {                    
                    $("#tableSearch").hide().show('slow');
                    bTestFirstTime = false;
                }

                if ($('#datatableResult').is(':hidden') && !bTestFirstTime) {
                    $('#datatableResult').show("slow").css("width", "100%");
                    $('#tableSearch').dataTable();
                }

                $("#pdfExport").show();

                var sData = JSON.stringify({ strFromDate: $("#fromDate").val(), strToDate: $("#toDate").val(), strTransactionID: $("#transactionID").val(), strOrderID: $("#orderID").val(), strCustomerID: $("#customerID").val(), strBasketState: $('#<%= basketState.ClientID %>').val() });

                $.ajax({
                    type: "POST",
                    url: sAjaxSourceUrl + 'Search',
                    data: sData,
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.d.ERROR) {
                            ShowError("error", data.d.ERROR, "alert alert-danger alert-dismissable");
                            //ShowError("error", $('#<%= lblError.ClientID %>').text());
                        }
                        else {
                            var parsed = $.parseJSON(data.d.OK);
                            $.each(parsed, function (i, jsondata) {
                                //  jsondata.img = "<img src='../assets/avatars/" + jsondata.img + ".png'  alt='tous' />";
                                //attribue l'image pour la colonne état
                                jsondata.etat = "<img src='../assets/images/" + jsondata.etat + ".png'  alt=" + jsondata.etat + " > </img>";

                            });

                            oTable = $('#tableSearch').dataTable({
                                "bDestroy": true,
                               // "bJQueryUI": true,
                                "bProcessing": true,
                                "bSort": true,
                                "bFilter": true,
                                "bAutoWidth": false,
                                "iDisplayLength": -1,
                                "aaData": parsed,
                                "bDeferRender": true,
                                "aoColumnDefs": [
			                        { "bSortable": false, "aTargets": [0] }
		                        ],
                                "aoColumns": [
                                     { "mDataProp": "img", "sClass": "imgcollapse" },
                                     { "mDataProp": "identite_id" },
                                     { "mDataProp": "panier_id" },
                                     { "mDataProp": "structure_id" },
                                     { "mDataProp": "sdate_operation" },
                                     { "mDataProp": "etat" },
                                     { "mDataProp": "commande_id" },
                                     { "mDataProp": "transaction_id" },
                                     { "mDataProp": "certificate" },
                                     { "mDataProp": "card_number" },
                                     { "mDataProp": "card_type" },
                                     { "mDataProp": "email" },
                                     { "mDataProp": "sdate_paiement", "sClass": "date_paiement" },
                                     { "mDataProp": "numero_billets" }
                                ],
                               
                            });

                           //Lorsque l'on clique sur l'image + pour afficher le détail
                            $('#tableSearch tbody td img.avatar').on('click', function () {
                                var nTr = this.parentNode.parentNode;

                                if (this.src.match('minus_green')) {
                                    /* This row is already open - close it */
                                    this.src = "../assets/images/button_plus_green.png";
                                    oTable.fnClose(nTr);
                                }
                                else {
                                    /* Open this row */
                                    this.src = "../assets/images/button_minus_green.png";
                                    oTable.fnOpen(nTr, fnFormatDetails(oTable, nTr), 'details');
                                }
                            });
                        }
                        // fnCallback($.parseJSON(data));
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert("Status: " + XMLHttpRequest.status + "\r\n" + textStatus + "\r\n" + errorThrown);
                    },
                    complete: function () {
                        //Simule le changement du nombre de résultat car par défaut on charge tout
                        $('#tableSearch_length option:eq(0)').attr('selected', 'selected');
                        $('#tableSearch_length select').change();
                    }
                });



                /* Formating function for row details */
                function fnFormatDetails(oTable, nTr) {
                    var aData = oTable.fnGetData(nTr);

                    var sData = JSON.stringify({ strPanierID: aData.panier_id });
                    var sOut = "";
                    jQuery.ajax({
                        url: sAjaxSourceUrl + 'Getpanier',
                        type: 'post',
                        data: sData,
                        async: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        success: function (data, textStatus, jqXHR) {

                            var json = $.parseJSON(data.d);
                            $(json).each(function (i, val) {

                                if (val.PANIER == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table width="100%">  <tr><th>PANIER</th></tr></table> ';
                                }
                                else {
                                    //Tableau PANIER
                                    sOut += '<table border="1" id="tb' + aData.panier_id + '" width="100%">   ';
                                    sOut += "<!-- En-tête du tableau --><tr><th colspan='11'>PANIER </th></tr><tr> <th>panier id</th><th>identite id</th><th>structure</th><th>date opération</th><th>etat</th><th>commande</th><th>transaction</th><th>certificate</th><th>card number</th><th>email</th><th>date paiement</th> </tr> ";
                                    sOut += " <!-- Corps du tableau --> ";

                                    $.each(val.PANIER, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.identite_id + '</td><td>' + v.structure_id + '</td><td>' + v.date_operation + '</td><td>' + v.etat + '</td><td>' + v.commande_id + '</td><td>' + v.transaction_id + '</td><td>' + v.certificate + '</td><td>' + v.card_number + '</td><td>' + v.email + '</td><td>' + v.date_paiement + '</td>';
                                    });

                                    sOut += "</table>";
                                }

                                if (val.PRODUIT == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table border="1" width="100%"> <tr><th>PRODUIT</th></tr></table>';
                                } else {
                                    //Tableau PRODUIT
                                    sOut += '<table border="1" id="tb' + aData.panier_entree_id + '"> ';
                                    sOut += "<!-- En-tête du tableau --><tr><th  colspan='22'>PRODUIT</th></tr><tr> <th>Numéro panier</th><th>manif id</th><th>entree id</th><th>vts id</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> ";
                                    sOut += " <!-- Corps du tableau --> ";

                                    $.each(val.PRODUIT, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                                    });

                                    sOut += " </table>";
                                }

                                if (val.FRAIS == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table border="1" width="100%">  <tr><th>FRAIS</th></tr></table>';
                                } else {
                                    //Tableau FRAIS
                                    sOut += '<table  border="1" id="tb' + aData.panier_entree_id + '" width="100%"> ';
                                    sOut += " <!-- En-tête du tableau --> <tr><th  colspan='14'>FRAIS</th></tr><tr> <th>panier_produit_id</th><th>panier_id</th><th>manif_id</th><th>seance_id</th><th>seance_description</th><th>produit_id</th><th>produit_nom</th><th>nombre</th><th>montant</th><th>frais</th><th>type_ligne</th><th>type_envoi</th><th>maquette_id</th><th>type_envoi_id</th></tr> ";
                                    sOut += "<!-- Corps du tableau --> ";

                                    $.each(val.FRAIS, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_produit_id + '</td><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.seance_id + '</td><td>' + v.seance_description + '</td><td>' + v.produit_id + '</td><td>' + v.produit_nom + '</td><td>' + v.nombre + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_ligne + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td>';
                                    });

                                    sOut += " </table>";
                                }


                                if (val.ENTREE_ABO == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table  border="1" width="100%">  <tr><th>ENTREE_ABO</th></tr> </table>';
                                } else {
                                    //Tableau ENTREE_ABO
                                    sOut += '<table border="1" id="tb' + aData.panier_entree_id + '" width="100%">';
                                    sOut += " <tr><th  colspan='23'>ENTREE_ABO</th></tr><tr> <th>Numéro panier</th><th>manif id</th><th>entree id</th><th>vts id</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> </thead>";
                                    sOut += " <!-- Corps du tableau --> ";

                                    $.each(val.ENTREE_ABO, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                                    });

                                    sOut += " </table>";
                                }


                                if (val.PRODUIT_ABO == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table  border="1"  width="100%">  <tr><th>PRODUIT_ABO</th></tr></table>';

                                } else {
                                    //Tableau PRODUIT_ABO
                                    sOut += '<table border="1" id="tb' + aData.panier_entree_id + '" width="100%"> </tr>';
                                    sOut += "<!-- En-tête du tableau --> <tr><th  colspan='23'>PRODUIT_ABO</th><tr> <th>Numéro panier</th><th>manif id</th><th>entree id</th><th>vts id</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> </thead>";
                                    sOut += " <!-- Corps du tableau --> ";

                                    $.each(val.PRODUIT_ABO, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                                    });

                                    sOut += " </table>";
                                }


                                if (val.FRAIS_ABO == null) {
                                    //Affiche le titre même si le tableau est vide
                                    sOut += '<table border="1"  width="100%">  ';
                                } else {
                                    //Tableau FRAIS_ABO
                                    sOut += '<table border="1"  id="tb' + aData.panier_entree_id + '" width="100%"> ';
                                    sOut += "<!-- En-tête du tableau --><tr><th  colspan='23'>FRAIS_ABO</th></tr><tr><th>FRAIS_ABO</th></tr></table><tr> <th>Numéro panier</th><th>manif id</th><th>entree id</th><th>vts id</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> </thead>";
                                    sOut += "<!-- Corps du tableau --> ";

                                    $.each(val.FRAIS_ABO, function (k, v) {
                                        sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                                    });

                                    sOut += "</table>";
                                }

                            });

                        }
                    });

                    return sOut;
                }
            }
        });


        //dans bibliotheque commune
        //Fonction qui affiche le message d'erreur dans l'id defini en paramètre
        //Le message se supprime au bout de 5 secondes
//        function ShowError(id, msg) {
//            $("#pdfExport").hide();
//            var errorDIV = ' <div class="alert alert-danger alert-dismissable" id="alertDIV">  <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>  ' + msg + '</div>';
//            $("#" + id).append(errorDIV).hide().show('slow');

//            //$('<%= lblError.ClientID %>').text().show();
//            $("#datatableResult").hide();
//            $(".alert").fadeOut(5000);

//        }


        /* Time between each scrolling frame */
//        $.fn.dataTableExt.oPagination.iTweenTime = 100;

//        $.fn.dataTableExt.oPagination.scrolling = {
//            "fnInit": function (oSettings, nPaging, fnCallbackDraw) {
//                var oLang = oSettings.oLanguage.oPaginate;
//                var oClasses = oSettings.oClasses;
//                var fnClickHandler = function (e) {
//                    if (oSettings.oApi._fnPageChange(oSettings, e.data.action)) {
//                        fnCallbackDraw(oSettings);
//                    }
//                };

//                var sAppend = (!oSettings.bJUI) ?
//            '<a class="' + oSettings.oClasses.sPagePrevDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button">' + oLang.sPrevious + '</a>' +
//            '<a class="' + oSettings.oClasses.sPageNextDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button">' + oLang.sNext + '</a>'
//            :
//            '<a class="' + oSettings.oClasses.sPagePrevDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button"><span class="' + oSettings.oClasses.sPageJUIPrev + '"></span></a>' +
//            '<a class="' + oSettings.oClasses.sPageNextDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button"><span class="' + oSettings.oClasses.sPageJUINext + '"></span></a>';
//                $(nPaging).append(sAppend);

//                var els = $('a', nPaging);
//                var nPrevious = els[0],
//            nNext = els[1];

//                oSettings.oApi._fnBindAction(nPrevious, { action: "previous" }, function () {
//                    /* Disallow paging event during a current paging event */
//                    if (typeof oSettings.iPagingLoopStart != 'undefined' && oSettings.iPagingLoopStart != -1) {
//                        return;
//                    }

//                    oSettings.iPagingLoopStart = oSettings._iDisplayStart;
//                    oSettings.iPagingEnd = oSettings._iDisplayStart - oSettings._iDisplayLength;

//                    /* Correct for underrun */
//                    if (oSettings.iPagingEnd < 0) {
//                        oSettings.iPagingEnd = 0;
//                    }

//                    var iTween = $.fn.dataTableExt.oPagination.iTweenTime;
//                    var innerLoop = function () {
//                        if (oSettings.iPagingLoopStart > oSettings.iPagingEnd) {
//                            oSettings.iPagingLoopStart--;
//                            oSettings._iDisplayStart = oSettings.iPagingLoopStart;
//                            fnCallbackDraw(oSettings);
//                            setTimeout(function () { innerLoop(); }, iTween);
//                        } else {
//                            oSettings.iPagingLoopStart = -1;
//                        }
//                    };
//                    innerLoop();
//                });

//                oSettings.oApi._fnBindAction(nNext, { action: "next" }, function () {
//                    /* Disallow paging event during a current paging event */
//                    if (typeof oSettings.iPagingLoopStart != 'undefined' && oSettings.iPagingLoopStart != -1) {
//                        return;
//                    }

//                    oSettings.iPagingLoopStart = oSettings._iDisplayStart;

//                    /* Make sure we are not over running the display array */
//                    if (oSettings._iDisplayStart + oSettings._iDisplayLength < oSettings.fnRecordsDisplay()) {
//                        oSettings.iPagingEnd = oSettings._iDisplayStart + oSettings._iDisplayLength;
//                    }

//                    var iTween = $.fn.dataTableExt.oPagination.iTweenTime;
//                    var innerLoop = function () {
//                        if (oSettings.iPagingLoopStart < oSettings.iPagingEnd) {
//                            oSettings.iPagingLoopStart++;
//                            oSettings._iDisplayStart = oSettings.iPagingLoopStart;
//                            fnCallbackDraw(oSettings);
//                            setTimeout(function () { innerLoop(); }, iTween);
//                        } else {
//                            oSettings.iPagingLoopStart = -1;
//                        }
//                    };
//                    innerLoop();
//                });
//            },

//            "fnUpdate": function (oSettings, fnCallbackDraw) {
//                if (!oSettings.aanFeatures.p) {
//                    return;
//                }

//                /* Loop over each instance of the pager */
//                var an = oSettings.aanFeatures.p;
//                for (var i = 0, iLen = an.length; i < iLen; i++) {
//                    if (an[i].childNodes.length !== 0) {
//                        an[i].childNodes[0].className =
//                    (oSettings._iDisplayStart === 0) ?
//                    oSettings.oClasses.sPagePrevDisabled : oSettings.oClasses.sPagePrevEnabled;

//                        an[i].childNodes[1].className =
//                    (oSettings.fnDisplayEnd() == oSettings.fnRecordsDisplay()) ?
//                    oSettings.oClasses.sPageNextDisabled : oSettings.oClasses.sPageNextEnabled;
//                    }
//                }
//            }
//        };




    </script>
</asp:Content>
