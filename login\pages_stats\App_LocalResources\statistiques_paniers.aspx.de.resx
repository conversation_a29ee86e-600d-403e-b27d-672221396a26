﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="basketStateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="btnSearchResource1.Text" xml:space="preserve">
    <value>bestätigen</value>
  </data>
  <data name="btnSearchResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblBasketIDResource1.Text" xml:space="preserve">
    <value>id Warenkorb</value>
  </data>
  <data name="lblBasketIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblBasketStateResource1.Text" xml:space="preserve">
    <value>Staatswagen</value>
  </data>
  <data name="lblBasketStateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblCardNumberResource1.Text" xml:space="preserve">
    <value>Keine Karte</value>
  </data>
  <data name="lblCardNumberResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblCardTypeResource1.Text" xml:space="preserve">
    <value>Karten-Typ</value>
  </data>
  <data name="lblCardTypeResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblCertificateResource1.Text" xml:space="preserve">
    <value>Zertifikat</value>
  </data>
  <data name="lblCertificateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblCommandIDResource1.Text" xml:space="preserve">
    <value>id Befehl</value>
  </data>
  <data name="lblCommandIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblCustomerIDResource1.Text" xml:space="preserve">
    <value>Kunden-ID</value>
  </data>
  <data name="lblCustomerIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblDateOpeResource1.Text" xml:space="preserve">
    <value>Zeitbetrieb</value>
  </data>
  <data name="lblDateOpeResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblDatePaiementResource1.Text" xml:space="preserve">
    <value>Zahlungsdatum</value>
  </data>
  <data name="lblDatePaiementResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblFromDateResource1.Text" xml:space="preserve">
    <value>Startdatum</value>
  </data>
  <data name="lblFromDateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblIDResource1.Text" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lblIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblMailResource1.Text" xml:space="preserve">
    <value>E-Mail</value>
  </data>
  <data name="lblMailResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblOrderIDResource1.Text" xml:space="preserve">
    <value>Bestell-Nummer</value>
  </data>
  <data name="lblOrderIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblStateResource1.Text" xml:space="preserve">
    <value>Zustand</value>
  </data>
  <data name="lblStateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblStructureIDResource1.Text" xml:space="preserve">
    <value>id Struktur</value>
  </data>
  <data name="lblStructureIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblToDateResource1.Text" xml:space="preserve">
    <value>Enddatum</value>
  </data>
  <data name="lblToDateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblTransactionIDResource1.Text" xml:space="preserve">
    <value>Keine Transaktion</value>
  </data>
  <data name="lblTransactionIDResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblTransactionIDTitleResource1.Text" xml:space="preserve">
    <value>Transaktions-ID</value>
  </data>
  <data name="lblTransactionIDTitleResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="ListItemResource1.Text" xml:space="preserve">
    <value>alle</value>
  </data>
  <data name="ListItemResource1.Value" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="ListItemResource2.Text" xml:space="preserve">
    <value>Validierte</value>
  </data>
  <data name="ListItemResource2.Value" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="ListItemResource3.Text" xml:space="preserve">
    <value>Bezahlt</value>
  </data>
  <data name="ListItemResource3.Value" xml:space="preserve">
    <value>P</value>
  </data>
  <data name="ListItemResource4.Text" xml:space="preserve">
    <value>Reserviert</value>
  </data>
  <data name="ListItemResource4.Value" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="ListItemResource5.Text" xml:space="preserve">
    <value>Ungültig</value>
  </data>
  <data name="ListItemResource5.Value" xml:space="preserve">
    <value>I</value>
  </data>
  <data name="ListItemResource6.Text" xml:space="preserve">
    <value>Erstellt</value>
  </data>
  <data name="ListItemResource6.Value" xml:space="preserve">
    <value>C</value>
  </data>
  <data name="PageResource1.Title" xml:space="preserve">
    <value>Themis Admin</value>
  </data>
  <data name="lblErrorDateResource1.Text" xml:space="preserve">
    <value>das Datum müssen ausgefüllt werden</value>
  </data>
  <data name="lblErrorDateResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblErrorResource1.Text" xml:space="preserve">
    <value />
  </data>
  <data name="lblErrorResource1.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lblNumeroBilletsResource1.Text" xml:space="preserve">
    <value>Ticket-Nr.</value>
  </data>
</root>