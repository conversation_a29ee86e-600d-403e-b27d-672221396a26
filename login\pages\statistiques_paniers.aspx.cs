﻿using iTextSharp.text;
using iTextSharp.text.pdf.qrcode;
using login.classes;
using login.wsThemisAdmin2010;
using Newtonsoft.Json;
using RestSharp;
using RestSharp.Serialization.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Web;
using System.Web.Services;
using System.Xml.Linq;
using utilitaires2010;
using utilitaires2010.sql.sqlserver;


namespace login.pages_stats
{
    public partial class statistiques_paniers : basePage
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        protected void Page_Load(object sender, EventArgs e)
        {

        }


        /// <summary>
        /// detail du panier
        /// </summary>
        /// <param name="strPanierID"></param>
        /// <returns></returns>
        [WebMethod]
        //  [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string Getpanier(string strPanierID)
        {
            MyConfigurationManager.SetUserLanguage();
            if (System.Web.HttpContext.Current.Session["idstructure"] != null && HttpContext.Current.Session["adminId"] != null)
            {
                string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Info("load statistique vente structureID " + strLastStructureId);

                Dictionary<string, DataTable> dict = new Dictionary<string, DataTable>();

                // Récupère le détail de la structure et du panier cliquer
                foreach (DataTable item in InitDetail(strLastStructureId, strPanierID))
                {
                    // ajoute les données de la datatable dans le dictionnaire (clé: nom de la table, value : les éléments)
                    dict.Add(item.TableName, item);
                }

                string Json = JsonConvert.SerializeObject(dict, Newtonsoft.Json.Formatting.None);
                return Json;
            }

            return "danger:aucune_structure_selectionnee";
        }

        /// <summary>
        /// Détail pour un panier et une structure
        /// </summary>
        /// <param name="StructureID">ID structure</param>
        /// <param name="panier_id"> ID panier</param>
        /// <returns></returns>
        protected static List<DataTable> InitDetail(string StructureID, string panier_id)
        {

            List<DataTable> lstTables = new List<DataTable>();
            log.Debug("Get TypeRun in Web.config");
            string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];


            //WebTracing2010.DBFunctions.ConnectWSAdmin
            SqlServerConnexion sqlConn = DBFunctions.ConnectWebTracing(typeRun);

            if (sqlConn == null)
            {
                log.Error("SQL conn est null dans InitDetail ");
            }

            try
            {

                DataSet dsOfStats = new DataSet();
                List<int[]> ListEventIDSeatID = new List<int[]>();
                DataSet dsListeLibelle = new DataSet();

                log.Debug("Etape 1");
                RequeteSelect reqS = new RequeteSelect();
                string sql = "SELECT top 5000 *, '' as sdate_operation, '' as sdate_paiement FROM PANIER WHERE  panier_id =" + panier_id;
                bool ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "PANIER");
                //identite_id
                //getIdentity => NOM /PRENOM
                wsThemis.WSThemisAbo WS = new wsThemis.WSThemisAbo();

                //TODO faire une methode dans wcf_themis
                //Iwcf_wsThemis wcfThemis = new Iwcf_wsThemisClient();

                if (dsOfStats.Tables["PANIER"] != null && dsOfStats.Tables["PANIER"].Rows.Count > 0)
                {
                    string strIdentityID = dsOfStats.Tables["PANIER"].Rows[0]["identite_id"].ToString();
                    string strIdentityNom = WS.GetNameCustomerProfile(StructureID, strIdentityID);
                    dsOfStats.Tables["PANIER"].Columns.Add("identite_nomprenom");
                    dsOfStats.Tables["PANIER"].Rows[0]["identite_nomprenom"] = strIdentityNom;
                    dsOfStats.AcceptChanges();
                }

                log.Debug("Etape 2");
                //entree
                sql = "SELECT * FROM PANIER_ENTREE WHERE  panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "ENTREE");
                if (dsOfStats.Tables["ENTREE"] != null && dsOfStats.Tables["ENTREE"].Rows.Count > 0)
                {
                    foreach (DataRow myrow in dsOfStats.Tables["ENTREE"].Rows)
                    {
                        int[] CoupleManifEntree = new int[2];
                        CoupleManifEntree[0] = Convert.ToInt32(myrow["manif_id"]);
                        CoupleManifEntree[1] = Convert.ToInt32(myrow["entree_id"]);
                        ListEventIDSeatID.Add(CoupleManifEntree);
                    }
                }

                log.Debug("Etape 3");
                sql = "SELECT * FROM PANIER_ENTREE_ABO WHERE  panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "ENTREE_ABO");
                if (dsOfStats.Tables["ENTREE_ABO"] != null && dsOfStats.Tables["ENTREE_ABO"].Rows.Count > 0)
                {
                    foreach (DataRow myrow in dsOfStats.Tables["ENTREE_ABO"].Rows)
                    {
                        int[] CoupleManifEntree = new int[2];
                        CoupleManifEntree[0] = Convert.ToInt32(myrow["manif_id"]);
                        CoupleManifEntree[1] = Convert.ToInt32(myrow["entree_id"]);
                        ListEventIDSeatID.Add(CoupleManifEntree);
                    }
                }
                //frais...
                sql = "SELECT * FROM PANIER_PRODUIT WHERE  type_ligne='FRAIS' and panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "FRAIS");
                sql = "SELECT * FROM PANIER_PRODUIT_ABO WHERE  type_ligne='FRAIS' and panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "FRAIS_ABO");
                //produits...
                sql = "SELECT * FROM PANIER_PRODUIT WHERE  type_ligne<>'FRAIS' and panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "PRODUIT");
                sql = "SELECT * FROM PANIER_PRODUIT_ABO WHERE type_ligne<>'FRAIS' and  panier_id =" + panier_id;
                ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "PRODUIT_ABO");

                log.Debug("Etape 4");
                //aller chercher les libelle zone section etage..
                if (ListEventIDSeatID.Count > 0)
                {
                    dsListeLibelle = WS.GetInformationOfEventIDSeatIDList(StructureID, ListEventIDSeatID.ToArray());

                    log.Debug("Etape 4.1");
                    //if (dsListeLibelle != null)
                    //{

                    //    log.Error("dsListeLibelle est null");
                    //    log.Debug("Etape 4.2");

                    //} else{
                    //    log.Debug("dsListeLibelle : " + dsListeLibelle.Tables[0].Rows.Count);
                    //}

                }

                log.Debug("Etape 5");
                if (dsOfStats.Tables["PANIER"] != null && dsOfStats.Tables["PANIER"].Rows.Count > 0)
                {
                    lstTables.Add(dsOfStats.Tables["PANIER"]);
                    log.Debug("lstTables : " + lstTables.Count);
                    //gvDetailPanier.DataSource = dsOfStats.Tables["PANIER"];
                    //gvDetailPanier.DataBind();
                }

                if (dsListeLibelle != null && dsListeLibelle.Tables.Count > 0)
                {

                    log.Error("if dsListeLibelle != null");
                    if (dsOfStats.Tables["ENTREE"] != null && dsOfStats.Tables["ENTREE"].Rows.Count > 0)
                    {
                        dsOfStats.Tables["ENTREE"].Columns.Add("section");
                        dsOfStats.Tables["ENTREE"].Columns.Add("zone");
                        dsOfStats.Tables["ENTREE"].Columns.Add("etage");
                        foreach (DataRow myrow in dsOfStats.Tables["ENTREE"].Rows)
                        {

                            foreach (DataRow myrowlibelle in dsListeLibelle.Tables[0].Select("manif_id=" + myrow["manif_id"].ToString() + " and entree_id=" + myrow["entree_id"].ToString()))
                            {
                                myrow["section"] = myrowlibelle["section"];
                                myrow["zone"] = myrowlibelle["zone"];
                                myrow["etage"] = myrowlibelle["etage"];
                            }


                            if (myrow["type_envoi_id"].GetType().Equals(DBNull.Value))
                            {
                                foreach (DataRow myrowlibelle in dsOfStats.Tables["FRAIS"].Select("produit_id=" + myrow["type_envoi_id"].ToString()))
                                {
                                    myrow["type_envoi"] = myrowlibelle["produit_nom"];
                                }
                            }
                        }
                        dsOfStats.AcceptChanges();


                        lstTables.Add(dsOfStats.Tables["ENTREE"]);
                        //gvDetailPanierEntree.DataSource = dsOfStats.Tables["ENTREE"];
                        //gvDetailPanierEntree.DataBind();
                    }

                }


                log.Info("avant foreach dsOfStats.Tables['PRODUIT'] : " + dsOfStats.Tables["PRODUIT"].Rows.Count);
                if (dsOfStats.Tables["PRODUIT"] != null && dsOfStats.Tables["PRODUIT"].Rows.Count > 0)
                {
                    foreach (DataRow myrow in dsOfStats.Tables["PRODUIT"].Rows)
                    {
                        foreach (DataRow myrowlibelle in dsOfStats.Tables["FRAIS"].Select("produit_id=" + myrow["type_envoi_id"].ToString()))
                        {
                            myrow["type_envoi"] = myrowlibelle["produit_nom"];
                        }
                    }

                    lstTables.Add(dsOfStats.Tables["PRODUIT"]);

                    //gvDetailPanierProduit.DataSource = dsOfStats.Tables["PRODUIT"];
                    //gvDetailPanierProduit.DataBind();
                }
                if (dsOfStats.Tables["FRAIS"] != null && dsOfStats.Tables["FRAIS"].Rows.Count > 0)
                {
                    lstTables.Add(dsOfStats.Tables["FRAIS"]);

                    //gvDetailPanierFrais.DataSource = dsOfStats.Tables["FRAIS"];
                    //gvDetailPanierFrais.DataBind();
                }

                if (dsOfStats.Tables["ENTREE_ABO"] != null && dsOfStats.Tables["ENTREE_ABO"].Rows.Count > 0)
                {
                    dsOfStats.Tables["ENTREE_ABO"].Columns.Add("section");
                    dsOfStats.Tables["ENTREE_ABO"].Columns.Add("zone");
                    dsOfStats.Tables["ENTREE_ABO"].Columns.Add("etage");
                    foreach (DataRow myrow in dsOfStats.Tables["ENTREE"].Rows)
                    {
                        foreach (DataRow myrowlibelle in dsListeLibelle.Tables[0].Select("manif_id=" + myrow["manif_id"].ToString() + " and entree_id=" + myrow["entree_id"].ToString()))
                        {

                            myrow["section"] = myrowlibelle["section"];
                            myrow["zone"] = myrowlibelle["zone"];
                            myrow["etage"] = myrowlibelle["etage"];

                        }
                        foreach (DataRow myrowlibelle in dsOfStats.Tables["FRAIS_ABO"].Select("produit_id=" + myrow["type_envoi_id"].ToString()))
                        {
                            myrow["type_envoi"] = myrowlibelle["produit_nom"];

                        }
                    }
                    dsOfStats.AcceptChanges();

                    lstTables.Add(dsOfStats.Tables["ENTREE_ABO"]);

                    //gvDetailPanierEntreeAbo.DataSource = dsOfStats.Tables["ENTREE_ABO"];
                    //gvDetailPanierEntreeAbo.DataBind();
                }

                log.Info("avant foreach dsOfStats.Tables['PRODUIT_ABO'] : " + dsOfStats.Tables["PRODUIT_ABO"].Rows.Count);
                if (dsOfStats.Tables["PRODUIT_ABO"] != null && dsOfStats.Tables["PRODUIT_ABO"].Rows.Count > 0)
                {
                    foreach (DataRow myrow in dsOfStats.Tables["PRODUIT_ABO"].Rows)
                    {

                        if (int.TryParse(myrow["type_envoi_id"].ToString(), out int type_envoi_id))
                        {
                            foreach (DataRow myrowlibelle in dsOfStats.Tables["FRAIS_ABO"].Select("produit_id=" + type_envoi_id))
                            {
                                myrow["type_envoi"] = myrowlibelle["produit_nom"];
                            }
                        }
                    }

                    lstTables.Add(dsOfStats.Tables["PRODUIT_ABO"]);

                    //gvDetailPanierProduitAbo.DataSource = dsOfStats.Tables["PRODUIT_ABO"];
                    //gvDetailPanierProduitAbo.DataBind();
                }
                if (dsOfStats.Tables["FRAIS_ABO"] != null && dsOfStats.Tables["FRAIS_ABO"].Rows.Count > 0)
                {
                    lstTables.Add(dsOfStats.Tables["FRAIS_ABO"]);

                    //gvDetailPanierFraisAbo.DataSource = dsOfStats.Tables["FRAIS_ABO"];
                    //gvDetailPanierFraisAbo.DataBind();
                }
            }
            catch (Exception ex)
            {
                log.Error("Catch de InitDetail " + ex.Message);
                throw new Exception(ex.Message);
            }
            finally
            {
                if (sqlConn != null)
                {
                    sqlConn.closeConnection();
                }
            }
            return lstTables;
        }


        /// <summary>
        /// Format la date pour les autres langues
        /// </summary>
        /// <param name="date_string">date format string</param>
        /// <returns>date parser ou date format MM/dd/yyyy HH:mm:ss</returns>
        private static string FormatDateString(string date_string)
        {
            if (!DateTime.TryParse(date_string, out DateTime date))
            {
                return date_string;
            }

            return date.ToString("dd/MM/yyyy HH:mm:ss");
        }

        /// <summary>
        /// regenere le print@home du panier
        /// </summary>
        /// <param name="strPanierID"></param>
        /// <returns></returns>
        [WebMethod]
        //  [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string GetPrintAtHome(string strPanierID, string strIdentiteID, int orderId)
        {
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Info("get print@home structureID=" + strLastStructureId + ",strPanierId=" + strPanierID);

                //string wspUrl = System.Configuration.ConfigurationManager.AppSettings["wsPaiement"];
                //login_WSThemisPaiement_WSThemisPaiement
                //string wspUrl = "http://localhost:1011/WSThemisPaiement.asmx";
                // wspaiem.Url = wspUrl;
                Thread.CurrentThread.CurrentCulture = CultureInfo.CreateSpecificCulture(HttpContext.Current.Request.UserLanguages[0]);
                string userLanguage = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName;

                //string reprintPDF = MyConfigurationManager.AppSettings("reprintPDF");

                int istructureId = int.Parse(strLastStructureId);

                string urlApiCustomer = MyConfigurationManager.AppSettings("apiCustomerUrl");
                string myToken = WapiCalls.GetToken(istructureId);

                string requestUrl = $"{urlApiCustomer}{istructureId}/webThemis/printHome/{userLanguage}/{orderId}";
                string apiPartenaireSalt = MyConfigurationManager.AppSettings("apiAuthentifSignature");
                string signature = WapiCalls.GeneratePartnerSignature("POST " + requestUrl, apiPartenaireSalt);

                RestClient client = new RestClient(requestUrl)
                {
                    Timeout = -1
                };
                RestRequest request = new RestRequest(Method.GET);

                request.AddHeader("Authorization", "Bearer " + myToken);
                request.AddHeader("Signature", signature);
                try
                {
                    IRestResponse response = client.Execute(request);
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        if (!string.IsNullOrEmpty(response.Content))
                        {
                            string result = response.Content.Replace("\"", string.Empty);
                            var mybytearray = Convert.FromBase64String(result);

                            //File.WriteAllBytes("D:\\Generation_test\\output.pdf", mybytearray);

                            return result;

                        }
                    }
                    else
                    {
                        if (response.StatusCode == HttpStatusCode.NotFound)
                        {
                            return $"danger:call_api_not_found";
                        }
                        else
                        {
                            return "danger:call_api";

                        }
                    }
                }
                catch (Exception)
                {
                    throw;
                }
               

            }

            return "danger:aucune_structure_selectionnee";
        }

        /// <summary>
        /// Lorsque l'on clique sur le bouton valider (page 'stats panier')
        /// </summary>
        /// <param name="strFromDate">date début</param>
        /// <param name="strToDate">date fin</param>
        /// <param name="strTransactionID">transaction ID (optionnel)</param>
        /// <param name="strOrderID">ID command (optionnel)</param>
        /// <param name="strCustomerID">ID client (optionnel)</param>
        /// <param name="strBasketState">etat panier</param>
        /// <returns></returns>
        [WebMethod]
        public static Dictionary<string, string> Search(string strFromDate, string strToDate, string strTransactionID, string strOrderID, string strCustomerID, string strBasketState, string strEmailAdress)
        {
            string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
            log.Info("typeRun " + typeRun);


            Dictionary<string, string> dictResult = new Dictionary<string, string>();
            SqlServerConnexion sqlConn = DBFunctions.ConnectWebTracing(typeRun);

            if (sqlConn == null)
            {
                log.Error("sqlConn est null pour DBFunctions.ConnectWebTracing(typeRun)");
            }


            try
            {
                if (System.Web.HttpContext.Current.Session["idstructure"] != null && System.Web.HttpContext.Current.Session["adminId"] != null)
                {

                    WSAdminClient wsAdmin = new WSAdminClient();

                    int structureID = Convert.ToInt32(System.Web.HttpContext.Current.Session["idstructure"].ToString());

                    int orderId = 0;
                    if (!string.IsNullOrEmpty(strOrderID))
                    {
                        orderId = Convert.ToInt32(strOrderID);
                    }
                    log.Info($"avant appel GetStatistiquePaniers {structureID} {FormatDateString(strFromDate)} {FormatDateString(strToDate)} {strTransactionID} {orderId} {strCustomerID}");

                    try
                    {

                        string sdtFinIso = ""; string sdtDebIso = "";
                        if (!string.IsNullOrEmpty(strFromDate) && !string.IsNullOrEmpty(strToDate))
                        {

                            DateTime dtFromDate = Convert.ToDateTime(strFromDate, DateTimeFormatInfo.InvariantInfo);
                            DateTime dtToDate = Convert.ToDateTime(strToDate, DateTimeFormatInfo.InvariantInfo);

                            sdtDebIso = dtFromDate.Year.ToString("0000") + dtFromDate.Month.ToString("00") + dtFromDate.Day.ToString("00") + " " + dtFromDate.Hour.ToString("00") + ":" + dtFromDate.Minute.ToString("00") + ":" + dtFromDate.Second.ToString("00");
                            sdtFinIso = dtToDate.Year.ToString("0000") + dtToDate.Month.ToString("00") + dtToDate.Day.ToString("00") + " " + dtToDate.Hour.ToString("00") + ":" + dtToDate.Minute.ToString("00") + ":" + dtToDate.Second.ToString("00");
                        }


                        string xmlDsOfStats = wsAdmin.GetStatistiquePaniers(structureID, sdtDebIso, sdtFinIso, strTransactionID, orderId, strCustomerID, strBasketState, strEmailAdress);
                        log.Info("après appel GetStatistiquePaniers " + xmlDsOfStats);

                        DataSet dsOfStats = DBFunctions.ConvertXMLToDataSet(xmlDsOfStats);

                        if (dsOfStats != null && dsOfStats.Tables.Count == 1)
                        {
                            log.Info("dsOfStats nb ligne " + dsOfStats.Tables[0].Rows.Count);

                            DataTableCustom dtCustom = new DataTableCustom();
                            // Ajoute au dictionnaire de resultat clé : OK , value les données converties en json
                            dictResult.Add("OK", dtCustom.ConvertDataTabletoString(dsOfStats.Tables[0]));

                            return dictResult;
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Error($"catch appel GetStatistiquePaniers {ex.StackTrace} {ex.Message}");

                        throw;
                    }


                    return null;
                }
                else
                {

                    return null;
                }

            }
            catch (Exception ex)
            {
                log.Error("Catch Search " + ex.Message);
                //ajoute dans le dictionnaire la clé "ERROR" et le message
                dictResult.Add("ERROR", ex.Message);
                return dictResult;
            }
            finally
            {
                if (sqlConn != null)
                {
                    sqlConn.closeConnection();
                }
            }

        }

        /// <summary>
        /// Lorsque l'on clique sur le bouton valider (page 'stats panier')
        /// </summary>
        /// <param name="strFromDate">date début</param>
        /// <param name="strToDate">date fin</param>
        /// <param name="strTransactionID">transaction ID (optionnel)</param>
        /// <param name="strOrderID">ID command (optionnel)</param>
        /// <param name="strCustomerID">ID client (optionnel)</param>
        /// <param name="strBasketState">etat panier</param>
        /// <returns></returns>
        [WebMethod]
        // [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static Dictionary<string, string> SearchOld(string strFromDate, string strToDate, string strTransactionID, string strOrderID, string strCustomerID, string strBasketState, string strEmailAdress)
        {
            string typeRun = System.Configuration.ConfigurationManager.AppSettings["TypeRun"];
            log.Info("typeRun " + typeRun);

            Dictionary<string, string> dictResult = new Dictionary<string, string>();
            SqlServerConnexion sqlConn = DBFunctions.ConnectWebTracing(typeRun);

            if (sqlConn == null)
            {
                log.Error("sqlConn est null pour DBFunctions.ConnectWebTracing(typeRun)");
            }

            try
            {
                string strWhereIdentityID = string.Empty, strWhereTransactionID = string.Empty, strWhereOrderID = string.Empty;
                string strWhereEmail = string.Empty;

                //récupère la structure ID en cookie
                if (System.Web.HttpContext.Current.Session["idstructure"] != null)
                {
                    string sql = string.Empty;

                    string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                    //int iOrderID = Convert.ToInt32(orderID);
                    //int iCustomerID = Convert.ToInt32(customerID);
                    int StructureID = Convert.ToInt32(strLastStructureId);

                    if (!string.IsNullOrEmpty(strCustomerID) && int.TryParse(strCustomerID, out int customerID) && customerID > 0)
                    {
                        strWhereIdentityID = " AND p.identite_id=" + customerID.ToString();
                    }
                    if (strOrderID != "" && int.TryParse(strOrderID, out int orderID) && orderID > 0)
                    {
                        strWhereOrderID = " AND commande_id=" + orderID.ToString();
                    }
                    if (strTransactionID != "")
                    {
                        strWhereTransactionID = " AND RTRIM(LTRIM(transaction_id)) ='" + strTransactionID.Trim() + "'";
                    }
                    if (strEmailAdress != "")
                    {
                        strWhereEmail = " AND email like '%" + strEmailAdress.Trim() + "%'";
                    }



                    if (!string.IsNullOrEmpty(strFromDate) && !string.IsNullOrEmpty(strToDate))
                    {
                        // convertit les date début et fin
                        strFromDate = FormatDateString(strFromDate);
                        strToDate = FormatDateString(strToDate);

                        /*
                                               // Defines a custom string format to display the DateTime value.
                                                 // zzzz specifies the full time zone offset.
                                                 String format = "MM/dd/yyyy hh:mm:ss";


                                              //reçois la date toujours dans le meme format
                                              CultureInfo en = new CultureInfo("en-US");
                                               Thread.CurrentThread.CurrentCulture = en;

                                                DateTime dtFromDate = DateTime.ParseExact(strFromDate,format,en.DateTimeFormat);
                                                DateTime dtToDate = DateTime.ParseExact(strToDate,format,en.DateTimeFormat);*/

                        DateTime dtFromDate = Convert.ToDateTime(strFromDate, DateTimeFormatInfo.InvariantInfo);
                        DateTime dtToDate = Convert.ToDateTime(strToDate, DateTimeFormatInfo.InvariantInfo);
                        //string sdtDebIso = dtFromDate.Year.ToString("0000") + dtFromDate.Month.ToString("00") + dtFromDate.Day.ToString("00");
                        //string sdtFinIso = dtToDate.Year.ToString("0000") + dtToDate.Month.ToString("00") + dtToDate.Day.ToString("00");

                        string sdtDebIso = dtFromDate.Year.ToString("0000") + dtFromDate.Month.ToString("00") + dtFromDate.Day.ToString("00") + " " + dtFromDate.Hour.ToString("00") + ":" + dtFromDate.Minute.ToString("00") + ":" + dtFromDate.Second.ToString("00");
                        string sdtFinIso = dtToDate.Year.ToString("0000") + dtToDate.Month.ToString("00") + dtToDate.Day.ToString("00") + " " + dtToDate.Hour.ToString("00") + ":" + dtToDate.Minute.ToString("00") + ":" + dtToDate.Second.ToString("00");

                        log.Info("convert dtFromDate " + dtFromDate + " en  sdtDebIso " + sdtDebIso);
                        log.Info("convert dtToDate " + dtToDate + " en  sdtFinIso " + sdtFinIso);

                        // si l'état du panier est différent de 'Tous' on prends en compte le panier ID sinon on supprime l'état du where 

                        sql = " DECLARE @datedeb varchar(18); set @datedeb='" + sdtDebIso + "';" + Environment.NewLine +
                      " DECLARE @datefin varchar(18);set @datefin='" + sdtFinIso + "';" + Environment.NewLine + Environment.NewLine +
                      " SELECT  TOP 1000 '' as img, '' as imgaction, p.identite_id, p.panier_id, p.structure_id, p.date_operation, '' as sdate_operation, p.etat, commande_id, transaction_id, certificate, card_number, card_type, email, date_paiement,'' as sdate_paiement " + Environment.NewLine +
                       ",web_user_id,haspdf = CASE WHEN sum(isnull(pe.maquette_id,0))  + SUM(isnull(pea.maquette_id, 0))  + SUM(isnull(pfte.maquette_id,0)) > 0 THEN 1 " + Environment.NewLine +
                            " ELSE 0 " + Environment.NewLine +
                            " END " + Environment.NewLine +
                       ", STUFF((SELECT DISTINCT ', ' + CAST(pe2.entree_id AS VARCHAR) FROM panier_entree pe2 WHERE pe2.panier_id = p.panier_id FOR XML PATH('')), 1, 2, '') + " + Environment.NewLine +
                       "  CASE WHEN EXISTS(SELECT 1 FROM panier_entree_abo pea2 WHERE pea2.panier_id = p.panier_id) THEN " + Environment.NewLine +
                       "    CASE WHEN EXISTS(SELECT 1 FROM panier_entree pe3 WHERE pe3.panier_id = p.panier_id) THEN ', ' ELSE '' END + " + Environment.NewLine +
                       "    STUFF((SELECT DISTINCT ', ' + CAST(pea3.entree_id AS VARCHAR) FROM panier_entree_abo pea3 WHERE pea3.panier_id = p.panier_id FOR XML PATH('')), 1, 2, '') " + Environment.NewLine +
                       "  ELSE '' END AS numero_billets " + Environment.NewLine +

                      "FROM panier p" + Environment.NewLine +
                       "LEFT OUTER JOIN panier_entree pe on p.panier_id=pe.panier_id" + Environment.NewLine +
                        "LEFT OUTER JOIN panier_entree_abo pea on p.panier_id=pea.panier_id" + Environment.NewLine +

                        "LEFT OUTER JOIN panier_formule_tarif pft on pft.panier_id = p.panier_id" + Environment.NewLine +
                        "LEFT OUTER JOIN panier_formule_tarif_entree pfte on pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id" + Environment.NewLine +
                        "INNER JOIN panier_request pr on pr.panier_id = p.panier_id " + Environment.NewLine +
                        "WHERE p.date_operation > cast(@datedeb as datetime) " + Environment.NewLine +
                        "AND p.date_operation < cast(@datefin as datetime) " + Environment.NewLine +
                        "AND p.structure_id =" + StructureID + Environment.NewLine;

                        if (strBasketState != "T")
                        {
                            sql += " AND p.etat='" + strBasketState + "'" + Environment.NewLine;
                        }
                        sql += strWhereIdentityID + strWhereTransactionID + strWhereOrderID + strWhereEmail +

                         " GROUP BY" + Environment.NewLine +
                         " p.identite_id, p.panier_id, " + Environment.NewLine +
                         " p.structure_id, p.date_operation, p.etat, commande_id, transaction_id, " + Environment.NewLine +
                         " certificate, card_number, card_type, email, date_paiement,web_user_id " + Environment.NewLine

                           + " ORDER BY panier_id DESC";
                    }
                    else
                    {

                        sql = " SELECT TOP 1000 '' as img, '' as imgaction, p.identite_id, p.panier_id, p.structure_id, p.date_operation, '' as sdate_operation, p.etat, commande_id, transaction_id, certificate, card_number, card_type, email, date_paiement,'' as sdate_paiement " + Environment.NewLine +
                    ",web_user_id,haspdf = CASE WHEN sum(isnull(pe.maquette_id,0))  + SUM(isnull(pea.maquette_id, 0))  + SUM(isnull(pfte.maquette_id,0)) > 0 THEN 1 " + Environment.NewLine +
                         " ELSE 0 " + Environment.NewLine +
                         " END " + Environment.NewLine +
                       ", STUFF((SELECT DISTINCT ', ' + CAST(pe2.entree_id AS VARCHAR) FROM panier_entree pe2 WHERE pe2.panier_id = p.panier_id FOR XML PATH('')), 1, 2, '') + " + Environment.NewLine +
                       "  CASE WHEN EXISTS(SELECT 1 FROM panier_entree_abo pea2 WHERE pea2.panier_id = p.panier_id) THEN " + Environment.NewLine +
                       "    CASE WHEN EXISTS(SELECT 1 FROM panier_entree pe3 WHERE pe3.panier_id = p.panier_id) THEN ', ' ELSE '' END + " + Environment.NewLine +
                       "    STUFF((SELECT DISTINCT ', ' + CAST(pea3.entree_id AS VARCHAR) FROM panier_entree_abo pea3 WHERE pea3.panier_id = p.panier_id FOR XML PATH('')), 1, 2, '') " + Environment.NewLine +
                       "  ELSE '' END AS numero_billets " + Environment.NewLine +

                   "FROM panier p" + Environment.NewLine +
                    "LEFT OUTER JOIN panier_entree pe on p.panier_id=pe.panier_id" + Environment.NewLine +
                     "LEFT OUTER JOIN panier_entree_abo pea on p.panier_id=pea.panier_id" + Environment.NewLine +

                        "LEFT OUTER JOIN panier_formule_tarif pft on pft.panier_id = p.panier_id" + Environment.NewLine +
                        "LEFT OUTER JOIN panier_formule_tarif_entree pfte on pft.panier_formule_tarif_id = pfte.panier_formule_tarif_id " + Environment.NewLine +
                        "INNER JOIN panier_request pr on pr.panier_id = p.panier_id " + Environment.NewLine +
                     "WHERE p.date_operation  < getdate() " + Environment.NewLine +
                        "AND p.structure_id =" + StructureID + Environment.NewLine;

                        if (strBasketState != "T")
                        {
                            sql += " AND p.etat='" + strBasketState + "'" + Environment.NewLine;
                        }
                        sql += strWhereIdentityID + strWhereTransactionID + strWhereOrderID + strWhereEmail +

                         " GROUP BY" + Environment.NewLine +
                         " p.identite_id, p.panier_id, " + Environment.NewLine +
                         " p.structure_id, p.date_operation, p.etat, commande_id, transaction_id, " + Environment.NewLine +
                         " certificate, card_number, card_type, email, date_paiement, web_user_id " + Environment.NewLine

                           + " ORDER BY panier_id DESC";
                    }

                    log.Info("sql : " + sql);
                    //WebTracing2010.DBFunctions.ConnectWSAdmin
                    DataSet dsOfStats = new DataSet();


                    RequeteSelect reqS = new RequeteSelect();

                    if (sqlConn == null || sqlConn.getCnx() == null)
                    {
                        Exception ex = new Exception("!! sql conn est null");
                        throw ex;
                    }

                    log.Info("Database : " + sqlConn.getCnx().Database);
                    log.Info("DataSource : " + sqlConn.getCnx().DataSource);
                    log.Info("ConnectionString : " + sqlConn.getCnx().ConnectionString);


                    bool ok = reqS.getReaderDataSet(sqlConn.getCnx(), sql, ref dsOfStats, "Stats");
                    if (dsOfStats != null && dsOfStats.Tables.Count == 1)
                    {
                        log.Info("dsOfStats nb ligne " + dsOfStats.Tables[0].Rows.Count);

                        DataTableCustom dtCustom = new DataTableCustom();

                        // Ajoute au dictionnaire de resultat clé : OK , value les données converties en json
                        dictResult.Add("OK", dtCustom.ConvertDataTabletoString(dsOfStats.Tables[0]));

                        return dictResult;
                    }

                }

                return null;
            }
            catch (Exception ex)
            {
                log.Error("Catch Search " + ex.Message);
                //ajoute dans le dictionnaire la clé "ERROR" et le message
                dictResult.Add("ERROR", ex.Message);
                return dictResult;
            }
            finally
            {
                if (sqlConn != null)
                {
                    sqlConn.closeConnection();
                }
            }
        }

        /// <summary>
        /// Télécharge le PDF
        /// </summary>
        /// <param name="status"></param>
        /// <param name="strFromDate">Date de début</param>
        /// <param name="strToDate">date de fin</param>
        /// <param name="strTransactionID">ID transaction</param>
        /// <param name="strOrderID">N° de commande</param>
        /// <param name="strCustomerID">N° client</param>
        /// <param name="strBasketState">etat panier</param>
        /// <returns>le nom du fichier</returns>
        [WebMethod(EnableSession = true)]
        public static string DownloadPDFFile(string status, string strFromDate, string strToDate, string strTransactionID, string strOrderID, string strCustomerID, string strBasketState)
        {
            string pdfName = ""; string pdfPath = ""; string pdfPathToSave = "";
            string currentUrl = Path.GetFileNameWithoutExtension(HttpContext.Current.Request.PhysicalPath);


            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                try
                {
                    string strLastStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                    string strdminId = HttpContext.Current.Session["adminId"].ToString();
                    string strdminName = HttpContext.Current.Session["adminName"].ToString();
                    pdfName = strLastStructureId + "_" + strdminId + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + currentUrl + ".pdf";

                    pdfPath = MyConfigurationManager.AppSettings("PdfFileDownloadStatsPaniers").Replace("[idstructure]", strLastStructureId);
                    pdfPathToSave = System.Web.HttpContext.Current.Server.MapPath(MyConfigurationManager.AppSettings("PdfFileDownloadStatsPaniers").Replace("[idstructure]", strLastStructureId));


                    if (!Directory.Exists(pdfPathToSave))
                    {
                        log.Debug("On créer le chemin " + pdfPathToSave);
                        Directory.CreateDirectory(pdfPathToSave);
                    }

                    pdfPath = Path.GetDirectoryName(pdfPath) + "\\" + pdfName;
                    pdfPathToSave = pdfPathToSave + pdfName;
                    log.Debug("pdfPath  : " + pdfPath);

                    //pdfPath = HttpContext.Current.Request.PhysicalApplicationPath + "pages\\pdf\\" + pdfName;


                    string str = "<body>";

                    //remplace les variables entre [] dans le template PDF
                    str += GestionTemplate.ReadFileTemplates("PDFTEMPLATE", HttpContext.Current.Request.UserLanguages[0], strLastStructureId)
                        .Replace("[date_edition]", DateTime.Now.ToString()).Replace("[admin_id]", strdminId).Replace("[admin_name]", strdminName).Replace("[tableau]", "")
                        .Replace("[start_date]", strFromDate).Replace("[end_date]", strToDate).Replace("[transaction_ID]", strTransactionID)
                        .Replace("[command_ID]", strOrderID).Replace("[customer_ID]", strCustomerID).Replace("[basket_state]", strBasketState);


                    str += "<table border='1' style='font-family:Times;font-size:9px;font-style:normal;'>" + status + "</table>";

                    //récupère les chemins des images (creer, valider, ... ) pour les etats
                    string pathC = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\C.png";
                    string pathV = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\V.png";
                    string pathP = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\P.png";
                    string pathR = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\R.png";
                    string pathI = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\I.png";


                    string pathPdf = HttpContext.Current.Request.PhysicalApplicationPath + "\\assets\\images\\pdf.png";

                    // Supprime les bouton + et - (détail)
                    str = str.Replace("\"", "'");
                    str = str.Replace("<img src='../assets/images/button_minus_green.png' class='detail'>", "");
                    str = str.Replace("<img src='../assets/images/button_plus_green.png' class='detail'>", "");

                    str = str.Replace("<img src='../assets/images/C.png' alt='C'>", "<img src='" + pathC + "' alt='Créé' />");
                    str = str.Replace("<img src='../assets/images/V.png' alt='V'>", "<img src='" + pathV + "' alt='Validé' />");
                    str = str.Replace("<img src='../assets/images/P.png' alt='P'>", "<img src='" + pathP + "' alt='Payé' />");
                    str = str.Replace("<img src='../assets/images/R.png' alt='R'>", "<img src='" + pathR + "' alt='Réservé' />");
                    str = str.Replace("<img src='../assets/images/I.png' alt='I'>", "<img src='" + pathI + "' alt='Invalidé' />");

                    str = str.Replace("<img src='../assets/images/pdf.png' class='redopdf' alt='pdf'>", "<img src='" + pathPdf + "' class='redopdf' alt='pdf'/>");

                    str += "</body>";


                    XDocument doc = XDocument.Parse(str);

                    //récupère tous les noeuds imgcollapse dans le header (th) firstAttribute
                    IEnumerable<XElement> hdFirstimgcollapse = from nm in doc.Elements("body").Elements("table").Elements("thead").Elements("tr").Elements("th")
                                                               where nm.FirstAttribute.Value.Contains("imgcollapse")
                                                               select nm;
                    hdFirstimgcollapse.Remove();

                    //récupère tous les noeuds imgcollapse dans le header (th) lastAttribute
                    IEnumerable<XElement> hdLastimgcollapse = from nm in doc.Elements("body").Elements("table").Elements("thead").Elements("tr").Elements("th")
                                                              where nm.LastAttribute.Value.Contains("imgcollapse")
                                                              select nm;
                    hdLastimgcollapse.Remove();

                    //sélectionne tous les "td" donc la class est imgcollapse == 1ere qui contenait le plus
                    IEnumerable<XElement> imgcollapse = from nm in doc.Elements("body").Elements("table").Elements("tbody").Elements("tr").Elements("td")
                                                        where nm.FirstAttribute.Value.Contains("imgcollapse")
                                                        select nm;
                    imgcollapse.Remove();


                    string strfinal = doc.ToString();

                    //    var strfinal = xmlD.OuterXml;
                    iTextSharpPDF.iTextSharpPDF textPDF = new iTextSharpPDF.iTextSharpPDF();

                    // Défini la taille du document PDF ainsi que les marges (top, bottom...)
                    Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 15f, 0f);


                    try
                    {

                        //  PdfWriter.GetInstance(pdfDoc, new FileStream(pdfPath, FileMode.Create));
                        //pdfDoc.Open();

                        //récupère le logo Rodrigue
                        string img = HttpContext.Current.Request.PhysicalApplicationPath + "pages\\pdf\\images\\logo_rod_index.png";

                        //créer le PDF avec la librairie iTextSharp située dans un projet custom (iTextSharpPDF)
                        pdfDoc = textPDF.CreateHeaderDocument(pdfDoc, "title", "subject", "key", "creator", "author", "headearname", "headercontent");
                        pdfDoc = textPDF.AddTextAndLogo(pdfDoc, pdfPathToSave, strfinal, img);

                    }
                    catch (DocumentException dex)
                    {
                        throw (dex);
                    }
                    catch (IOException ioex)
                    {
                        throw (ioex);
                    }
                    finally
                    {
                        pdfDoc.Close();
                    }

                    // retour le nom du ficher
                    log.Debug("return : " + MyConfigurationManager.AppSettings("PdfFileDownloadStatsPaniers").Replace("[idstructure]", strLastStructureId) + pdfName);
                    return MyConfigurationManager.AppSettings("PdfFileDownloadStatsPaniers").Replace("[idstructure]", strLastStructureId) + pdfName;

                }
                catch (Exception ex)
                {
                    log.Debug("catch DownloadPDFFile : " + ex.Message);
                    throw new Exception("catch DownloadPDFFile : " + ex.Message);
                }
            }

            return "danger:aucune_structure_selectionnee";
        }

    }
}