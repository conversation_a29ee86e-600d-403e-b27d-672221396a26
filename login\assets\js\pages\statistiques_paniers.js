﻿var bTestFirstTime = true;
var oTable;
var sAjaxSourceUrl = "statistiques_paniers.aspx/";

$(document).ready(function() {
    LoadPage();
});

function LoadPage() {

    $('#datatableResult').hide();
    $('#pdfExport').hide();

    //Désactive le second input' par défaut
    // $('#toDate').prop('disabled', true);

    //    var logic = function (currentDateTime) {

    //        //attribue au input "toDate" la date saisie en minimum et en maximum celle d'aujourd'hui
    //        $('#toDate').datetimepicker({
    //            minDate: currentDateTime,
    //            maxDate: '0'
    //        });

    //        //Active le second input' par défaut au changement de la 1ere
    //        //$('#toDate').prop('disabled', false);

    //        //Si la valeur saisie est inférieur à la valeur du 2ème input on le vide
    ////        if (currentDateTime < $('#toDate').val()) {
    ////            $('#toDate').val("");
    ////        } else {
    //            //récupère la date d'aujourd'hui
    //            $("#toDate").datepicker("setDate", new Date());
    //            //récupère la langue situé dans le champs caché
    //            var lang = $("#dateLang").val();
    //            //convertit le format de date
    //            $.datepicker.setDefaults($.datepicker.regional[lang]);
    //       // }
    //    };

    //   $('#toDate').datetimepicker({ dateFormat: 'dd/mm/yy' });
    //   $('#fromDate').datetimepicker({ dateFormat: 'dd/mm/yy' });


    $('#toDate').datetimepicker();
    $('#fromDate').datetimepicker();
    convertToEnglishDate($("#fromDate").val());


    $("#fromDate").on('change', function() {
        var currentdate = new Date();
        //var startDatetime = getLocaleShortDateString(currentdate) + " 00:00";
        var endDatetime = formatDate(currentdate) + " " + currentdate.timeNow();


        // var l = navigator.language ? navigator.language : navigator['userLanguage'];
        // var endDatetime = currentdate.toLocaleDateString(l)+ " " + currentdate.timeNow();
        $('#toDate').val(endDatetime);
    });


}




$("#pdfExport").on('click', function() {
    //var basketSelected =  $('#<%= basketState.ClientID %> :selected').text();   MainContent_basketState
    var sData = JSON.stringify({ status: oTable.html(), strFromDate: $("#fromDate").val(), strToDate: $("#toDate").val(), strTransactionID: $("#transactionID").val(), strOrderID: $("#orderID").val(), strCustomerID: $("#customerID").val(), strBasketState: $('#MainContent_basketState :selected').text() });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'DownloadPDFFile',
        data: sData,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function(response) {
            window.open(window.location.origin + response.d, response.d, 'width=1024,height=768,toolbar=no,location=no,directories=no,status=no,menubar=no,copyhistory=no')
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }
    });

});


/*
var btnSearch = "<%= btnSearch.ClientID %>";
var lblErrorDate = "<%= lblErrorDate.ClientID %>";
*/
$('#btnSearch').click(function() {

    //            if ($("#fromDate").val() == "" || $("#toDate").val() == "") {
    //                ShowError("error", $('#MainContent_lblErrorDate').text(), "alert alert-danger alert-dismissable");
    //            }
    //            else {
    if (bTestFirstTime) {
        $("#tableSearch").hide().show('slow');
        bTestFirstTime = false;
    }

    if ($('#datatableResult').is(':hidden') && !bTestFirstTime) {
        $('#datatableResult').show("slow").css("width", "100%");
        $('#tableSearch').dataTable();
    }

    $("#pdfExport").show();


    var usFromDate = convertToEnglishDate($("#fromDate").val());
    var usToDate = convertToEnglishDate($("#toDate").val());

    //var basket = $('#<%= basketState.ClientID %>').val(); 
    var sData = JSON.stringify({
        strFromDate: usFromDate,
        strToDate: usToDate,
        strTransactionID: $("#transactionID").val(),
        strOrderID: $("#orderID").val(),
        strCustomerID: $("#customerID").val(),
        strBasketState: $('#basketState').val(),
        strEmailAdress: $("#emailAdress").val()
    });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'Search',
        data: sData,
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function(data) {
            if (data.d.ERROR) {
                ShowError("error", data.d.ERROR, "alert alert-danger alert-dismissable");
                //ShowError("error", $('#<%= lblError.ClientID %>').text());
            } else {
                var parsed = $.parseJSON(data.d.OK);
                $.each(parsed, function(i, jsondata) {
                    //  jsondata.img = "<img src='../assets/avatars/" + jsondata.img + ".png'  alt='tous' />";
                    //attribue l'image pour la colonne état
                    jsondata.etat = "<img src='../assets/images/" + jsondata.etat + ".png'  alt='" + jsondata.etat + "' > </img>";
                    if (jsondata.haspdf == "1" && jsondata.commande_id != null)
                        jsondata.haspdf = "<img src='../assets/images/pdf.png' class='redopdf' alt='pdf' > </img>";
                    else
                        jsondata.haspdf = "";

                    if (/VIREMENT/i.test(jsondata.certificate) || /VIREMENT/i.test(jsondata.card_type)) {
                        jsondata.certificate = ReadXmlTranslate("lbl_virement");
                    }
                });

                oTable = $('#tableSearch').dataTable({
                    "bDestroy": true,
                    // "bJQueryUI": true,
                    "bProcessing": true,
                    "bSort": true,
                    "bFilter": true,
                    "bAutoWidth": false,
                    "iDisplayLength": -1,
                    "aaData": parsed,
                    "bDeferRender": true,
                    "aoColumnDefs": [
                        { "bSortable": false, "aTargets": [0] }
                    ],
                    "aoColumns": [
                        { "mDataProp": "img", "sClass": "imgcollapse" },
                        { "mDataProp": "web_user_id" },
                        { "mDataProp": "identite_id" },
                        { "mDataProp": "panier_id" },
                        { "mDataProp": "structure_id" },
                        //{ "mDataProp": "sdate_operation" },
                        {
                            "mDataProp": "date_operation",
                            "sClass": "center",
                            "mRender": function(data, full) {
                                var browserLanguage = GetNavigatorLanguage().split('-')[0];
                                moment.locale(browserLanguage)
                                return moment(data).format('llll')
                            }
                        },
                        { "mDataProp": "etat" },
                        { "mDataProp": "commande_id" },
                        { "mDataProp": "transaction_id" },
                        { "mDataProp": "certificate" },
                        {
                            "mDataProp": "pay_request",
                            "sClass": "center",
                            "mRender": function(url, type, full) {

                                var htmlAction = '<span data-trad="lbl_' + full.pay_request + '"></span>';


                                return htmlAction;
                            }
                        },
                        //{ "mDataProp": "pay_request" },
                        { "mDataProp": "card_number" },
                        { "mDataProp": "card_type" },
                        { "mDataProp": "email" },
                        //{ "mDataProp": "sdate_paiement", "sClass": "date_paiement" },
                        {
                            "mDataProp": "date_paiement",
                            "sClass": "center",
                            "mRender": function(data, full) {
                                if(data == null)
                                    return "-";
                                
                                var browserLanguage = GetNavigatorLanguage().split('-')[0];
                                moment.locale(browserLanguage)
                                return moment(data).format('llll')
                            }
                        },
                        { "mDataProp": "haspdf" },
                        { "mDataProp": "numero_billets" }
                    ],

                });


                //Lorsque l'on clique sur l'image + pour afficher le détail
                $('#tableSearch tbody td img.detail').on('click', function() {
                    var nTr = this.parentNode.parentNode;

                    if (this.src.match('minus_green')) {
                        /* This row is already open - close it */
                        this.src = "../assets/images/button_plus_green.png";
                        oTable.fnClose(nTr);
                    } else {
                        /* Open this row */
                        this.src = "../assets/images/button_minus_green.png";
                        oTable.fnOpen(nTr, fnFormatDetails(oTable, nTr), 'details');
                    }
                });

                  //Lorsque l'on clique sur l'image pour refaire le print@home
                  $('#tableSearch tbody td img.redopdf').on('click', function() {
                    var nTr = this.parentNode.parentNode;

                    /* Open this row */
                    var aData = oTable.fnGetData(nTr);
                    //var strPanierID= aData.panier_id ;
                    // call regeneration print@home
                    //public static string GetPrintAtHome(string strPanierID)

                    //var sData = JSON.stringify({ strPanierID: aData[3], strIdentiteID: aData[2], options: "LANGCODE=" + GetNavigatorLanguage() });
                    var sData = JSON.stringify({ strPanierID: aData[3], strIdentiteID: aData[2], orderId: parseInt(aData[7]) });
                    var sOut = "";
                    jQuery.ajax({
                        url: sAjaxSourceUrl + 'GetPrintAtHome',
                        type: 'post',
                        data: sData,
                        async: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        success: function (data, textStatus, jqXHR) {
                            console.log(data);
                            dowloadFileFromBase64(data.d, "tickets");
                        },
                        error: function(XMLHttpRequest, textStatus, errorThrown) {
                            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
                        },
                        complete: function() {
                            //Simule le changement du nombre de résultat car par défaut on charge tout

                        }
                    });

                });

                //Change la lange du datatable
                SetLangeDataTable(oTable, "tableSearch");
            }


            // fnCallback($.parseJSON(data));
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        },
        complete: function() {
            //Simule le changement du nombre de résultat car par défaut on charge tout
            $('#tableSearch_length option:eq(0)').attr('selected', 'selected');
            $('#tableSearch_length select').change();

            LaunchTraduction();
        }
    });
    function dowloadFileFromBase64(base64, fileName) {

        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = "data:application/pdf;base64," + base64;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /* Formating function for row details */
    function fnFormatDetails(oTable, nTr) {
        var aData = oTable.fnGetData(nTr);

        //                    var sData = JSON.stringify({ strPanierID: aData.panier_id });
        var sData = JSON.stringify({ strPanierID: aData[3] });
        var sOut = "";
        jQuery.ajax({
            url: sAjaxSourceUrl + 'Getpanier',
            type: 'post',
            data: sData,
            async: false,
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            success: function(data, textStatus, jqXHR) {

                if (data.d.split(':')[0] != "danger") {

                    var json = $.parseJSON(data.d);
                    $(json).each(function(i, val) {

                        if (val.PANIER == null) {
                            //Affiche le titre même si le tableau est vide
                            sOut += '<table width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_panier") + '</th></tr></table> ';
                        } else {
                            //Tableau PANIER
                            sOut += '<table border="1" id="tb' + aData.panier_id + '" width="100%">   ';
                            sOut += "<!-- En-tête du tableau --><tr><th colspan='11'>" + ReadXmlTranslate("lbl_title_panier") + " </th></tr><tr> <th>" + ReadXmlTranslate("lbl_col_panier_id") + "</th><th>" + ReadXmlTranslate("identite_id") + "</th><th>" + ReadXmlTranslate("structure_name") + "</th><th>" + ReadXmlTranslate("date_operation") + "</th><th>" + ReadXmlTranslate("lbl_col_etat") + "</th><th>" + ReadXmlTranslate("lbl_col_commande") + "</th><th>" + ReadXmlTranslate("lbl_col_transaction") + "</th><th>" + ReadXmlTranslate("lbl_col_certificate") + "</th><th>" + ReadXmlTranslate("lbl_col_card_number") + "</th><th>" + ReadXmlTranslate("email") + "</th><th>" + ReadXmlTranslate("paiement_date") + "</th> </tr> ";
                            sOut += " <!-- Corps du tableau --> ";

                            $.each(val.PANIER, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.identite_id + '</td><td>' + v.structure_id + '</td><td>' + v.date_operation + '</td><td>' + v.etat + '</td><td>' + v.commande_id + '</td><td>' + v.transaction_id + '</td><td>' + v.certificate + '</td><td>' + v.card_number + '</td><td>' + v.email + '</td><td>' + v.date_paiement + '</td>';
                            });

                            sOut += "</table>";
                        }

                        if (val.ENTREE == null) {
                            sOut += '<table border="1" width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_entree") + '</th></tr></table>';
                        } else {
                            sOut += '<table border="1" id="tb' + aData.panier_entree_id + '" width="100%">';
                            sOut += " <tr><th  colspan='23'>" + ReadXmlTranslate("lbl_title_entree") + "</th></tr><tr> <th>" + ReadXmlTranslate("lbl_col_basket_number") + "</th><th>" + ReadXmlTranslate("manifestation_id") + "</th><th>" + ReadXmlTranslate("lbl_col_entree_id") + "</th><th>" + ReadXmlTranslate("lbl_col_vts_id") + "</th><th>" + ReadXmlTranslate("lbl_col_rang") + "</th><th>" + ReadXmlTranslate("lbl_col_siege") + "</th><th>" + ReadXmlTranslate("col_seance_id") + "</th><th>" + ReadXmlTranslate("col_categorie_id") + "</th><th>" + ReadXmlTranslate("col_type_tarif_id") + "</th><th>" + ReadXmlTranslate("col_manifestation_nom") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("col_categ_nom") + "</th><th>" + ReadXmlTranslate("col_type_tarif_nom") + "</th><th>" + ReadXmlTranslate("col_montant1") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th><th>" + ReadXmlTranslate("col_consumer_id") + "</th><th>" + ReadXmlTranslate("col_valeur") + "</th><th>" + ReadXmlTranslate("col_section") + "</th><th>" + ReadXmlTranslate("col_zone") + "</th><th>" + ReadXmlTranslate("col_etage") + "</th> </tr> </thead>";
                            sOut += " <!-- Corps du tableau --> ";

                            $.each(val.ENTREE, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                            });

                            sOut += " </table>";
                        }


                        if (val.PRODUIT == null) {
                            //Affiche le titre même si le tableau est vide
                            sOut += '<table border="1" width="100%"> <tr><th>' + ReadXmlTranslate("lbl_title_produit") + '</th></tr></table>';
                        } else {
                            //Tableau PRODUIT
                            sOut += '<table border="1" id="tb' + aData.panier_entree_id + '"> ';
                            //sOut += "<!-- En-tête du tableau --><tr><th  colspan='22'>"+ReadXmlTranslate("lbl_title_produit")+"</th></tr><tr> <th>"+ReadXmlTranslate("lbl_col_basket_number")+"</th><th>"+ReadXmlTranslate("manifestation_id")+"</th><th>"+ReadXmlTranslate("lbl_col_entree_id")+"</th><th>"+ReadXmlTranslate("lbl_col_vts_id")+"</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> ";
                            sOut += " <tr><th  colspan='22'>" + ReadXmlTranslate("lbl_title_produit") + "</th></tr><tr> <th>" + ReadXmlTranslate("lbl_col_basket_number") + "</th><th>" + ReadXmlTranslate("manifestation_id") + "</th><th>" + ReadXmlTranslate("lbl_col_entree_id") + "</th><th>" + ReadXmlTranslate("lbl_col_vts_id") + "</th><th>" + ReadXmlTranslate("lbl_col_rang") + "</th><th>" + ReadXmlTranslate("lbl_col_siege") + "</th><th>" + ReadXmlTranslate("col_seance_id") + "</th><th>" + ReadXmlTranslate("col_categorie_id") + "</th><th>" + ReadXmlTranslate("col_type_tarif_id") + "</th><th>" + ReadXmlTranslate("col_manifestation_nom") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("col_categ_nom") + "</th><th>" + ReadXmlTranslate("col_type_tarif_nom") + "</th><th>" + ReadXmlTranslate("col_montant1") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th><th>" + ReadXmlTranslate("col_consumer_id") + "</th><th>" + ReadXmlTranslate("col_valeur") + "</th><th>" + ReadXmlTranslate("col_section") + "</th><th>" + ReadXmlTranslate("col_zone") + "</th><th>" + ReadXmlTranslate("col_etage") + "</th> </tr> </thead>";
                            sOut += " <!-- Corps du tableau --> ";

                            $.each(val.PRODUIT, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                            });

                            sOut += " </table>";
                        }

                        if (val.FRAIS == null) {
                            //Affiche le titre même si le tableau est vide
                            sOut += '<table border="1" width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_frais") + '</th></tr></table>';
                        } else {
                            //Tableau FRAIS
                            sOut += '<table  border="1" id="tb' + aData.panier_entree_id + '" width="100%"> ';
                            sOut += " <!-- En-tête du tableau --> <tr><th  colspan='14'>" + ReadXmlTranslate("lbl_title_frais") + "</th></tr><tr> <th>" + ReadXmlTranslate("col_panier_produit_id") + "</th><th>" + ReadXmlTranslate("lbl_col_panier_id") + "</th><th>" + ReadXmlTranslate("manifestation_id") + "</th><th>" + ReadXmlTranslate("seance_id") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("product_id") + "</th><th>" + ReadXmlTranslate("product_nom") + "</th><th>" + ReadXmlTranslate("col_nombre") + "</th><th>" + ReadXmlTranslate("col_montant") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("type_ligne") + "</th><th>" + ReadXmlTranslate("col_type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th></tr> ";
                            sOut += "<!-- Corps du tableau --> ";

                            $.each(val.FRAIS, function(k, v) {
                                sOut += '<tr><td>' + v.panier_produit_id + '</td><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.seance_id + '</td><td>' + v.seance_description + '</td><td>' + v.produit_id + '</td><td>' + v.produit_nom + '</td><td>' + v.nombre + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_ligne + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td>';
                            });

                            sOut += " </table>";
                        }


                        if (val.ENTREE_ABO == null) {
                            //Affiche le titre même si le tableau est vide
                            sOut += '<table  border="1" width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_entree_abo") + '</th></tr> </table>';
                        } else {
                            //Tableau ENTREE_ABO
                            sOut += '<table border="1" id="tb' + aData.panier_entree_id + '" width="100%">';
                            sOut += " <tr><th  colspan='23'>" + ReadXmlTranslate("lbl_title_entree_abo") + "</th></tr><tr> <th>" + ReadXmlTranslate("col_panier_numero") + "</th><th>" + ReadXmlTranslate("col_manif_id") + "</th><th>" + ReadXmlTranslate("lbl_col_entree_id") + "</th><th>" + ReadXmlTranslate("lbl_col_vts_id") + "</th><th>" + ReadXmlTranslate("lbl_col_rang") + "</th><th>" + ReadXmlTranslate("lbl_col_siege") + "</th><th>" + ReadXmlTranslate("col_seance_id") + "</th><th>" + ReadXmlTranslate("col_categ_id") + "</th><th>" + ReadXmlTranslate("col_type_tarif_id") + "</th><th>" + ReadXmlTranslate("col_manifestation_nom") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("col_categ_nom") + "</th><th>" + ReadXmlTranslate("col_type_tarif_nom") + "</th><th>" + ReadXmlTranslate("col_montant1") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("col_type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th><th>" + ReadXmlTranslate("col_consumer_id") + "</th><th>" + ReadXmlTranslate("col_valeur") + "</th><th>" + ReadXmlTranslate("col_section") + "</th><th>" + ReadXmlTranslate("col_zone") + "</th><th>" + ReadXmlTranslate("col_etage") + "</th> </tr> </thead>";
                            sOut += " <!-- Corps du tableau --> ";

                            $.each(val.ENTREE_ABO, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                            });

                            sOut += " </table>";
                        }


                        if (val.PRODUIT_ABO == null) {
                            //Affiche le titre même si le tableau est vide
                            sOut += '<table  border="1"  width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_produit_abo") + '</th></tr></table>';

                        } else {
                            //Tableau PRODUIT_ABO
                            sOut += '<table border="1" id="tb' + aData.panier_entree_id + '" width="100%"> </tr>';
                            sOut += "<!-- En-tête du tableau --> <tr><th  colspan='23'>" + ReadXmlTranslate("lbl_title_produit_abo") + "</th><tr> <th>" + ReadXmlTranslate("col_panier_numero") + "</th><th>" + ReadXmlTranslate("col_manif_id") + "</th><th>" + ReadXmlTranslate("lbl_col_entree_id") + "</th><th>" + ReadXmlTranslate("lbl_col_vts_id") + "</th><th>" + ReadXmlTranslate("lbl_col_rang") + "</th><th>" + ReadXmlTranslate("lbl_col_siege") + "</th><th>" + ReadXmlTranslate("col_seance_id") + "</th><th>" + ReadXmlTranslate("col_categorie_id") + "</th><th>" + ReadXmlTranslate("col_type_tarif_id") + "</th><th>" + ReadXmlTranslate("col_manifestation_nom") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("col_categ_nom") + "</th><th>" + ReadXmlTranslate("col_type_tarif_nom") + "</th><th>" + ReadXmlTranslate("col_montant1") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("col_type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th><th>" + ReadXmlTranslate("col_consumer_id") + "</th><th>" + ReadXmlTranslate("col_valeur") + "</th><th>" + ReadXmlTranslate("col_section") + "</th><th>" + ReadXmlTranslate("col_zone") + "</th><th>" + ReadXmlTranslate("col_etage") + "</th> </tr> </thead>";
                            sOut += " <!-- Corps du tableau --> ";

                            $.each(val.PRODUIT_ABO, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                            });

                            sOut += " </table>";
                        }


                        if (val.FRAIS_ABO == null) {
                            //Affiche le titre même si le tableau est vide
                            // sOut += '<table border="1"  width="100%">  ';
                            sOut += '<table  border="1"  width="100%">  <tr><th>' + ReadXmlTranslate("lbl_title_frais_abo") + '</th></tr></table>';
                        } else {
                            //Tableau FRAIS_ABO
                            sOut += '<table border="1"  id="tb' + aData.panier_entree_id + '" width="100%"> ';
                            // sOut += "<!-- En-tête du tableau --><tr><th  colspan='23'>FRAIS_ABO</th></tr><tr><th>FRAIS_ABO</th></tr></table><tr> <th>Numéro panier</th><th>manif id</th><th>entree id</th><th>vts id</th><th>rang</th><th>siege</th><th>seance id</th><th>categ id</th><th>type tarif id</th><th>manif nom</th><th>seance description</th><th>categ nom</th><th>type tarif nom</th><th>montant</th><th>frais</th><th>type envoi</th><th>maquette id</th><th>type envoi id</th><th>consumer id</th><th>valeur</th><th>section</th><th>zone</th><th>etage</th> </tr> </thead>";
                            sOut += "<!-- En-tête du tableau --><tr><th  colspan='23'>" + ReadXmlTranslate("lbl_title_frais_abo") + "</th></tr><tr><th>" + ReadXmlTranslate("col_frais_abo") + "</th></tr></table><tr> <th>" + ReadXmlTranslate("col_panier_numero") + "</th><th>" + ReadXmlTranslate("col_manif_id") + "</th><th>" + ReadXmlTranslate("lbl_col_entree_id") + "</th><th>" + ReadXmlTranslate("lbl_col_vts_id") + "</th><th>" + ReadXmlTranslate("lbl_col_rang") + "</th><th>" + ReadXmlTranslate("lbl_col_siege") + "</th><th>" + ReadXmlTranslate("col_seance_id") + "</th><th>" + ReadXmlTranslate("col_categorie_id") + "</th><th>" + ReadXmlTranslate("col_type_tarif_id") + "</th><th>" + ReadXmlTranslate("col_manifestation_nom") + "</th><th>" + ReadXmlTranslate("lbl_col_description_seance") + "</th><th>" + ReadXmlTranslate("col_categ_nom") + "</th><th>" + ReadXmlTranslate("col_type_tarif_nom") + "</th><th>" + ReadXmlTranslate("col_montant1") + "</th><th>" + ReadXmlTranslate("col_frais") + "</th><th>" + ReadXmlTranslate("col_type_envoi") + "</th><th>" + ReadXmlTranslate("maquette_id") + "</th><th>" + ReadXmlTranslate("col_type_envoi_id") + "</th><th>" + ReadXmlTranslate("col_consumer_id") + "</th><th>" + ReadXmlTranslate("col_valeur") + "</th><th>" + ReadXmlTranslate("col_section") + "</th><th>" + ReadXmlTranslate("col_zone") + "</th><th>" + ReadXmlTranslate("col_etage") + "</th> </tr> </thead>";
                            sOut += "<!-- Corps du tableau --> ";

                            $.each(val.FRAIS_ABO, function(k, v) {
                                sOut += '<tr><td>' + v.panier_id + '</td><td>' + v.manif_id + '</td><td>' + v.entree_id + '</td><td>' + v.vts_id + '</td><td>' + v.rang + '</td><td>' + v.siege + '</td><td>' + v.seance_id + '</td><td>' + v.categ_id + '</td><td>' + v.type_tarif_id + '</td><td>' + v.manif_nom + '</td><td>' + v.seance_description + '</td><td>' + v.categ_nom + '</td><td>' + v.type_tarif_nom + '</td><td>' + v.montant + '</td><td>' + v.frais + '</td><td>' + v.type_envoi + '</td><td>' + v.maquette_id + '</td><td>' + v.type_envoi_id + '</td><td>' + v.consumer_id + '</td><td>' + v.valeur + '</td><td>' + v.section + '</td><td>' + v.zone + '</td><td>' + v.etage + '</td>';
                            });

                            sOut += "</table>";
                        }

                    });
                } else {
                    //sinon dans data.d = Aucune structure sélectionnée
                    ShowModalError("modalMessage", "erreur", data.d.split(':')[1], "alert alert-danger alert-dismissable", 3000);
                }
            }
        });

        return sOut;
    }
    //}//fin else verif date
});


//dans bibliotheque commune
//Fonction qui affiche le message d'erreur dans l'id defini en paramètre
//Le message se supprime au bout de 5 secondes
//        function ShowError(id, msg) {
//            $("#pdfExport").hide();
//            var errorDIV = ' <div class="alert alert-danger alert-dismissable" id="alertDIV">  <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>  ' + msg + '</div>';
//            $("#" + id).append(errorDIV).hide().show('slow');

//            //$('<%= lblError.ClientID %>').text().show();
//            $("#datatableResult").hide();
//            $(".alert").fadeOut(5000);

//        }


/* Time between each scrolling frame */
//        $.fn.dataTableExt.oPagination.iTweenTime = 100;

//        $.fn.dataTableExt.oPagination.scrolling = {
//            "fnInit": function (oSettings, nPaging, fnCallbackDraw) {
//                var oLang = oSettings.oLanguage.oPaginate;
//                var oClasses = oSettings.oClasses;
//                var fnClickHandler = function (e) {
//                    if (oSettings.oApi._fnPageChange(oSettings, e.data.action)) {
//                        fnCallbackDraw(oSettings);
//                    }
//                };

//                var sAppend = (!oSettings.bJUI) ?
//            '<a class="' + oSettings.oClasses.sPagePrevDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button">' + oLang.sPrevious + '</a>' +
//            '<a class="' + oSettings.oClasses.sPageNextDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button">' + oLang.sNext + '</a>'
//            :
//            '<a class="' + oSettings.oClasses.sPagePrevDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button"><span class="' + oSettings.oClasses.sPageJUIPrev + '"></span></a>' +
//            '<a class="' + oSettings.oClasses.sPageNextDisabled + '" tabindex="' + oSettings.iTabIndex + '" role="button"><span class="' + oSettings.oClasses.sPageJUINext + '"></span></a>';
//                $(nPaging).append(sAppend);

//                var els = $('a', nPaging);
//                var nPrevious = els[0],
//            nNext = els[1];

//                oSettings.oApi._fnBindAction(nPrevious, { action: "previous" }, function () {
//                    /* Disallow paging event during a current paging event */
//                    if (typeof oSettings.iPagingLoopStart != 'undefined' && oSettings.iPagingLoopStart != -1) {
//                        return;
//                    }

//                    oSettings.iPagingLoopStart = oSettings._iDisplayStart;
//                    oSettings.iPagingEnd = oSettings._iDisplayStart - oSettings._iDisplayLength;

//                    /* Correct for underrun */
//                    if (oSettings.iPagingEnd < 0) {
//                        oSettings.iPagingEnd = 0;
//                    }

//                    var iTween = $.fn.dataTableExt.oPagination.iTweenTime;
//                    var innerLoop = function () {
//                        if (oSettings.iPagingLoopStart > oSettings.iPagingEnd) {
//                            oSettings.iPagingLoopStart--;
//                            oSettings._iDisplayStart = oSettings.iPagingLoopStart;
//                            fnCallbackDraw(oSettings);
//                            setTimeout(function () { innerLoop(); }, iTween);
//                        } else {
//                            oSettings.iPagingLoopStart = -1;
//                        }
//                    };
//                    innerLoop();
//                });

//                oSettings.oApi._fnBindAction(nNext, { action: "next" }, function () {
//                    /* Disallow paging event during a current paging event */
//                    if (typeof oSettings.iPagingLoopStart != 'undefined' && oSettings.iPagingLoopStart != -1) {
//                        return;
//                    }

//                    oSettings.iPagingLoopStart = oSettings._iDisplayStart;

//                    /* Make sure we are not over running the display array */
//                    if (oSettings._iDisplayStart + oSettings._iDisplayLength < oSettings.fnRecordsDisplay()) {
//                        oSettings.iPagingEnd = oSettings._iDisplayStart + oSettings._iDisplayLength;
//                    }

//                    var iTween = $.fn.dataTableExt.oPagination.iTweenTime;
//                    var innerLoop = function () {
//                        if (oSettings.iPagingLoopStart < oSettings.iPagingEnd) {
//                            oSettings.iPagingLoopStart++;
//                            oSettings._iDisplayStart = oSettings.iPagingLoopStart;
//                            fnCallbackDraw(oSettings);
//                            setTimeout(function () { innerLoop(); }, iTween);
//                        } else {
//                            oSettings.iPagingLoopStart = -1;
//                        }
//                    };
//                    innerLoop();
//                });
//            },

//            "fnUpdate": function (oSettings, fnCallbackDraw) {
//                if (!oSettings.aanFeatures.p) {
//                    return;
//                }

//                /* Loop over each instance of the pager */
//                var an = oSettings.aanFeatures.p;
//                for (var i = 0, iLen = an.length; i < iLen; i++) {
//                    if (an[i].childNodes.length !== 0) {
//                        an[i].childNodes[0].className =
//                    (oSettings._iDisplayStart === 0) ?
//                    oSettings.oClasses.sPagePrevDisabled : oSettings.oClasses.sPagePrevEnabled;

//                        an[i].childNodes[1].className =
//                    (oSettings.fnDisplayEnd() == oSettings.fnRecordsDisplay()) ?
//                    oSettings.oClasses.sPageNextDisabled : oSettings.oClasses.sPageNextEnabled;
//                    }
//                }
//            }
//        };